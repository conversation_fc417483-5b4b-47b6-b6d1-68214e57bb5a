# Files in the config/locales directory are used for internationalization and
# are automatically loaded by Rails. If you want to use locales other than
# English, add the necessary files in this directory.
#
# To use the locales, use `I18n.t`:
#
#     I18n.t "hello"
#
# In views, this is aliased to just `t`:
#
#     <%= t("hello") %>
#
# To use a different locale, set it with `I18n.locale`:
#
#     I18n.locale = :es
#
# This would use the information in config/locales/es.yml.
#
# To learn more about the API, please read the Rails Internationalization guide
# at https://guides.rubyonrails.org/i18n.html.
#
# Be aware that YAML interprets the following case-insensitive strings as
# booleans: `true`, `false`, `on`, `off`, `yes`, `no`. Therefore, these strings
# must be quoted to be interpreted as strings. For example:
#
#     en:
#       "yes": yup
#       enabled: "ON"

en:
  hello: "Hello world"

  controllers:
    errors:
      no_organiser: "No associated organiser found"
      unauthorized: "Unauthorized access"
      cannot_delete_event: "Cannot delete event with existing orders."
      event_destroy_error: "Event could not be destroyed: %{message}"
      promo_code_not_found: "Promo code not found."
      unauthorized_organiser: "Unauthorized access. Requires organiser role."
      promo_code_used: "Cannot delete a promo code that has been used (%{count}). Consider deactivating it instead."
      promo_code_destroy_error: "Promo code could not be destroyed."
      organiser_required: "Organiser context required."
      promo_code_cant_be_blank: "Promo code cannot be blank."
      promo_code_error_unexpected_apply: "An unexpected error occurred while applying the promo code."
      promo_code_error_unexpected_remove: "An unexpected error occurred while removing the promo code."
      no_order_found: "Order not found."
      record_not_found: "The requested resource was not found."
      record_not_destroyed: "The resource could not be deleted."
      foreign_key_constraint: "Cannot delete this resource because it is referenced by other data."
      parameter_missing: "Required parameter '%{parameter}' is missing."
      invalid_argument: "Invalid argument provided."
      internal_server_error: "An unexpected error occurred. Please try again later."

  auth:
    invalid_credentials: "Error: Invalid username or password. Please try again."
    login_success: "Logged in successfully."
    logout_success: "logged out successfully"
    no_active_session: "Couldn't find an active session."
    signup_success: "Signed up successfully."
    user_create_error: "User couldn't be created successfully. %{errors}"

  models:
    event:
      end_time_error: "must be after the start time"
      invalid_tag_ids: "contains invalid tag IDs: %{ids}"
      social_media_format: "must be an array of JSON objects with 'platform' and 'link'"
      policies_format: "must be an array of JSON objects with 'type' and 'details'"

    promo_code:
      max_uses_below_current: "cannot be set below current usage (%{count})"
      valid_until_in_past: "cannot be set in the past"
      invalid_event_ids: "include invalid or unauthorized IDs: %{ids}"
      invalid_ticket_type_ids: "include invalid or unauthorized IDs: %{ids}"
      valid_until_before_from: "must be after valid_from"
      discount_value_exceeds_100: "cannot exceed 100% for percentage discounts"
      event_ids_not_array: "must be an array"
      ticket_type_ids_not_array: "must be an array"

  model:
    errors:
      promo_code_processing: "Order must be in processing status to apply promo code."
      promo_code_not_found: "Promo code not found or not available."
      promo_code_n_a: "Promo code is not applicable to this order."
      promo_code_save_fail: "Failed to save order with promo code: %{message}"
      promo_code_apply_fail: "Failed to apply promo code: %{message}"
      promo_code_remove_fail: "Order must be in processing status to remove promo code."
      no_promo_code_applied: "No promo code is currently applied to this order."
      promo_code_order_save_fail: "Failed to save order after removing promo code: %{message}"
      can_not_remove_promo_code: "Cannot remove promo code: %{message}"
      total_cant_be_negative: "Order total cannot be negative or zero."
      profile_picture_invalid: "Profile picture is not valid or you don't have permission."

  contact:
    email_sent_success: "Email sent successfully"
    missing_required_parameters: "Missing required parameters"

  mailer:
    welcome:
      subject: "Registration Successful!"
      greeting: "Hello %{name},"
      success_message: "You have successfully registered with TicketPie!"
      excitement_message: "We're excited to have you on board. Now you can explore and purchase tickets for your favorite events."
      best_regards: "Best regards,"
      team_name: "The TicketPie Team"
      contact_us: "Contact us at"

  errors:
    insufficient_available_amount: "Insufficient available amount"

  services:
    order_creation:
      event_id_missing: "Event ID is missing"
      basket_empty: "Customer basket is empty or missing"
      no_applicable_items: "No applicable items found in the basket for the selected event"

  errors:
    insufficient_available_amount: "Insufficient available amount"

  pdf:
    ticket:
      header: "YOUR TICKET"
      instruction: "Please show it on your phone when you arrive at the venue"
      ticket_type: "Ticket Type:"
      first_name: "First Name"
      last_name: "Last Name"
      event: "Event"
      venue: "Venue"
      date: "Date"
      time: "Time"


