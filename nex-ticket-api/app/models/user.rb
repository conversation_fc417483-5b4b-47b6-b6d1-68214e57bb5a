class User < ApplicationRecord
  include Rodauth::Rails.model

  enum :status, { unverified: 1, verified: 2, closed: 3 }

  validates :email, presence: true, if: :not_visitor?
  validates :email, uniqueness: { conditions: -> { where(status: [ 1, 2 ]).where.not(email: nil) } }, if: :not_visitor?
  validates :first_name, presence: true, if: :not_visitor?
  validates :last_name, presence: true, if: :not_visitor?

  def is_organiser?
    type == "OrganiserAccount"
  end

  def is_customer?
    type == "Customer"
  end

  def is_scanner?
    type == "ScannerAccount"
  end

  def is_admin?
    false
  end

  def user_type
    case type
    when "OrganiserAccount"
      "organiser"
    when "Customer"
      "customer"
    when "ScannerAccount"
      "scanner"
    end
  end

  def visitor?
    email.blank? && visitor_token.present?
  end

  def not_visitor?
    !visitor?
  end

  def email_required?
    not_visitor?
  end

  def password_required?
    not_visitor?
  end
end
