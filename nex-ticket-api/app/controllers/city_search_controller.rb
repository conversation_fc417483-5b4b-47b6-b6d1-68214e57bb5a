# app/controllers/city_search_controller.rb
class CitySearchController < ApplicationController
  before_action :set_city, only: %i[ show ]

  SERIALIZER_INCLUDES = [ "state", "timezone" ]
  def index
    lang = I18n.locale.to_s

    if params[:q].present?
      query = params[:q]
      contains_pattern = "%#{query}%"
      starts_with_pattern = "#{query}%"

      quoted_starts_with = ActiveRecord::Base.connection.quote(starts_with_pattern)
      quoted_lang = ActiveRecord::Base.connection.quote("name:#{lang}")

      priority_sql = <<-SQL.squish
        CASE
          WHEN name ILIKE #{quoted_starts_with} THEN 1
          WHEN other_names->>#{quoted_lang} ILIKE #{quoted_starts_with} THEN 1
          ELSE 2
        END
      SQL

      @cities = City
        .select("cities.*", "(#{priority_sql}) AS sort_priority")
        .where("other_names->>'name:#{lang}' ILIKE ? OR name ILIKE ?", contains_pattern, contains_pattern)
        .order("sort_priority ASC, name ASC")
    elsif params[:lat].present? && params[:lon].present?
      city = get_city_with_location(params[:lat], params[:lon])
      serializer = CitySerializer.new(city, include: SERIALIZER_INCLUDES)
      render json: serializer.serializable_hash
      return
    else
      @cities = City.order(basic_relevance: :desc)
    end

    render json: @cities
  end

  # GET /cities/1
  def show
    serializer = CitySerializer.new(@city, include: SERIALIZER_INCLUDES)
    render json: serializer.serializable_hash
  end

  # GET /cities/current
  def get_current
    ip_address = request.remote_ip
    geoapify_service = GeoapifyGeocoding.new(ip_address)
    address_info = geoapify_service.fetch_address
    if address_info.present?
      city_name = address_info.dig("city", "names", "en")
      city = City.where("other_names->>'name:en' = ?", city_name)
      render json: { id: city[0].id }
    else
      raise "City not found: #{city_name}"
    end
  end

private
  def set_city
    @city = City.find(params[:id])
  end

  def get_city_with_location(lat, lon)
    service = GeoapifyReverseGeocoding.new(lat, lon)
    address_info = service.fetch_address

    if address_info.present?
      city_name = address_info.dig("features", 0, "properties", "city")
      cities = City.where("other_names->>'name:en' = ?", city_name)
      if cities.count == 1
        city = cities.first
        city
      elsif cities.count > 1
        raise "Multiple cities found with the name: #{city_name}"
      else
        raise "City not found: #{city_name}"
      end
    end
  end
end
