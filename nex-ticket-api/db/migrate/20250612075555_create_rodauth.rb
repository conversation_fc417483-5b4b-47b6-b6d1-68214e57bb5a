class CreateRodauth < ActiveRecord::Migration[8.0]
  def change
    enable_extension "citext"

    # Add Rodauth columns to existing users table
    add_column :users, :status, :integer, null: false, default: 1
    add_column :users, :password_hash, :string

    # Remove Devise-specific columns
    remove_column :users, :encrypted_password, :string
    remove_column :users, :jti, :string
    remove_column :users, :reset_password_token, :string
    remove_column :users, :reset_password_sent_at, :datetime
    remove_column :users, :sign_in_count, :integer
    remove_column :users, :current_sign_in_at, :datetime
    remove_column :users, :last_sign_in_at, :datetime
    remove_column :users, :current_sign_in_ip, :string
    remove_column :users, :last_sign_in_ip, :string
    remove_column :users, :confirmation_token, :string
    remove_column :users, :confirmed_at, :datetime
    remove_column :users, :confirmation_sent_at, :datetime
    remove_column :users, :unconfirmed_email, :string
    remove_column :users, :failed_attempts, :integer
    remove_column :users, :unlock_token, :string
    remove_column :users, :locked_at, :datetime

    # Update email column to use citext and allow NULL
    change_column :users, :email, :citext, null: true

    # Handle duplicate empty emails by setting them to NULL for visitor accounts
    execute "UPDATE users SET email = NULL WHERE email = '' AND visitor_token IS NOT NULL"

    # Add unique index on email for active users (excluding NULL emails)
    add_index :users, :email, unique: true, where: "status IN (1, 2) AND email IS NOT NULL", name: "index_users_on_email_active"

    # Used by the password reset feature
    create_table :user_password_reset_keys, id: false do |t|
      t.bigint :id, primary_key: true
      t.foreign_key :users, column: :id
      t.string :key, null: false
      t.datetime :deadline, null: false
      t.datetime :email_last_sent, null: false, default: -> { "CURRENT_TIMESTAMP" }
    end

    # Used by the account verification feature
    create_table :user_verification_keys, id: false do |t|
      t.bigint :id, primary_key: true
      t.foreign_key :users, column: :id
      t.string :key, null: false
      t.datetime :requested_at, null: false, default: -> { "CURRENT_TIMESTAMP" }
      t.datetime :email_last_sent, null: false, default: -> { "CURRENT_TIMESTAMP" }
    end

    # Used by the verify login change feature
    create_table :user_login_change_keys, id: false do |t|
      t.bigint :id, primary_key: true
      t.foreign_key :users, column: :id
      t.string :key, null: false
      t.string :login, null: false
      t.datetime :deadline, null: false
    end

    # Used by the remember me feature
    create_table :user_remember_keys, id: false do |t|
      t.bigint :id, primary_key: true
      t.foreign_key :users, column: :id
      t.string :key, null: false
      t.datetime :deadline, null: false
    end
  end
end
